<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckEnvironment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'env:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check which environment file is currently being used';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $basePath = base_path();
        $localEnvExists = file_exists($basePath . '/.env.local');
        $defaultEnvExists = file_exists($basePath . '/.env');
        
        $this->info('Environment Configuration Check');
        $this->info('================================');
        
        // Check which files exist
        $this->line('Environment Files Status:');
        $this->line('- .env.local: ' . ($localEnvExists ? '✓ EXISTS' : '✗ NOT FOUND'));
        $this->line('- .env: ' . ($defaultEnvExists ? '✓ EXISTS' : '✗ NOT FOUND'));
        
        // Determine which file should be loaded
        if ($localEnvExists) {
            $this->info('✓ Using: .env.local (local development configuration)');
            $currentEnvFile = '.env.local';
        } elseif ($defaultEnvExists) {
            $this->warn('⚠ Using: .env (default configuration)');
            $currentEnvFile = '.env';
        } else {
            $this->error('✗ No environment file found!');
            return 1;
        }
        
        // Display current environment values
        $this->line('');
        $this->info('Current Environment Configuration:');
        $this->line('- APP_NAME: ' . config('app.name'));
        $this->line('- APP_ENV: ' . config('app.env'));
        $this->line('- APP_URL: ' . config('app.url'));
        $this->line('- DB_CONNECTION: ' . config('database.default'));
        $this->line('- DB_HOST: ' . config('database.connections.' . config('database.default') . '.host'));
        $this->line('- DB_DATABASE: ' . config('database.connections.' . config('database.default') . '.database'));
        
        // Show environment file being used from config
        if (config('app.current_env_file')) {
            $this->line('- Current ENV File: ' . config('app.current_env_file'));
        }
        
        $this->line('');
        $this->info('Environment check completed successfully!');
        
        return 0;
    }
}
