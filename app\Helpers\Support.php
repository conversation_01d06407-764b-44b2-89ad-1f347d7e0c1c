<?php

/**
 * Get the current environment file being used
 *
 * @return string
 */
function getCurrentEnvFile()
{
    $basePath = base_path();
    $localEnvFile = $basePath . '/.env.local';

    if (file_exists($localEnvFile)) {
        return '.env.local';
    }

    return '.env';
}

/**
 * Check if local environment file is being used
 *
 * @return bool
 */
function isUsingLocalEnv()
{
    return getCurrentEnvFile() === '.env.local';
}

/**
 * Get environment file path
 *
 * @return string
 */
function getEnvFilePath()
{
    return base_path(getCurrentEnvFile());
}

function is_uuid($uuid) {
    $re = '/^[a-z0-9\-]{36}$/i';
    preg_match_all($re, $uuid, $matches, PREG_SET_ORDER, 0);
    if(!$matches){
        $response = responseFail(trans('messages.check_uuid-fail'),['uuid'=> $uuid]);
        $return = response()->json($response, 500, [], JSON_PRETTY_PRINT);
        $return->throwResponse();
    }
}
