<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Load environment-specific configuration
        $this->loadEnvironmentConfiguration();
    }

    /**
     * Load environment configuration based on available files
     *
     * @return void
     */
    private function loadEnvironmentConfiguration()
    {
        $basePath = base_path();
        $localEnvFile = $basePath . '/.env.local';
        $defaultEnvFile = $basePath . '/.env';

        // Determine which environment file to use
        $envFile = file_exists($localEnvFile) ? '.env.local' : '.env';

        // Store the current environment file being used
        config(['app.current_env_file' => $envFile]);

        // Log which environment file is being used (for debugging)
        if (app()->environment('local') && config('app.debug')) {
            \Log::info("Environment file loaded: {$envFile}");
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        Schema::defaultStringLength(191); // Update defaultStringLength
        //prevent host header attack
        $allowed_host = array('127.0.0.1:8000','devoprex.sig.id','www.oprex.sig.id','oprex.sig.id','www.oprex.sig.id','**********','**********','localhost:8080');
        // dd($_SERVER['HTTP_HOST']);
        //Khusus akses via browser 
        if (!app()->runningInConsole() && (!isset($_SERVER['HTTP_HOST']) || !in_array($_SERVER['HTTP_HOST'], $allowed_host))) 
        {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            exit;
        }
    }
}
