<?php

namespace App\Http\Middleware;

use Closure;

class EnvironmentInfo
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        // Only add environment info in local development and debug mode
        if (app()->environment('local') && config('app.debug')) {
            $envFile = getCurrentEnvFile();
            $envInfo = "<!-- Environment: {$envFile} -->";
            
            // Add environment info to HTML responses
            if ($response->headers->get('Content-Type') && 
                strpos($response->headers->get('Content-Type'), 'text/html') !== false) {
                
                $content = $response->getContent();
                $content = str_replace('</head>', $envInfo . "\n</head>", $content);
                $response->setContent($content);
            }
        }
        
        return $response;
    }
}
